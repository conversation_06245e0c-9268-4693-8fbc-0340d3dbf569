.container {
  padding: 0 0 120rpx 0;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部整体区域 */
.header-wrapper {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #69c0ff 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  position: relative;
  z-index: 100;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 26rpx;
  position: relative;
  z-index: 2;
}

.location-wrapper {
  display: flex;
  align-items: center;
}

.navbar-location {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* Tab容器 */
.tab-container {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 筛选条件 */
.filter-container {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 内容区域 */
.content-area {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 统计信息卡片 */
.statistics-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 500;
}

.stat-value.arrear {
  color: #ff4757;
}

/* 楼栋列表 */
.building-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.building-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.building-item:last-child {
  border-bottom: none;
}

/* 房屋列表 */
.house-list {
  padding: 20rpx;
  background: #fafafa;
}

.house-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: space-between;
}

.house-card {
  width: calc(50% - 50rpx);
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  border: 2rpx solid transparent;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-card.arrear {
  border-color: #ff4757;
  background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
}

.house-card.normal {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #fff 100%);
}

.house-unit {
  margin-bottom: 16rpx;
}

.unit-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.house-amount {
  text-align: center;
  margin-top: auto;
}

.amount-text {
  font-size: 24rpx;
  font-weight: 500;
  display: block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.amount-text.arrear {
  color: #fff;
  background: #ff4757;
}

.amount-text.normal {
  color: #fff;
  background: #52c41a;
}

/* 加载和空状态 */
.house-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  background: #fafafa;
}

.house-empty {
  padding: 40rpx;
  background: #fafafa;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 车位内容（预留） */
.parking-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.coming-soon {
  text-align: center;
}

/* Vant组件样式覆盖 */
.van-collapse-item__content {
  padding: 0 !important;
}

.van-dropdown-menu {
  box-shadow: none;
}

.van-dropdown-menu__bar {
  height: 88rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.van-tabs__line {
  background-color: #1890ff;
}

.van-tab--active {
  color: #1890ff;
}
