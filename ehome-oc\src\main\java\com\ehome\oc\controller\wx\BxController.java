package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.config.ServerConfig;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/wx/bx")
public class BxController extends BaseWxController {

    @Value("${ruoyi.profile}")
    private String profilePath;

    @Autowired
    private ServerConfig serverConfig;

    @PostMapping("/addData")
    public AjaxResult addData() {
        try {
            Long wxUserId = getCurrentUserId();
            JSONObject params = getParams();
            String businessId = params.getString("businessId");
            String recordId = Seq.getId();

            Record record = new Record();
            record.set("type",params.getString("type"));
            record.set("content",params.getString("content"));
            record.set("address",params.getString("address"));
            record.set("name",params.getString("name"));
            record.set("phone",params.getString("phone"));
            record.set("media_urls",params.getString("media_urls"));
            record.set("id", recordId);
            record.set("community_id", getCurrentUser().getCommunityId());
            record.set("wx_user_id", wxUserId);
            record.set("owner_id", getCurrentUser().getOwnerId());
            record.set("create_time", DateUtils.getTime());
            record.set("update_time", DateUtils.getTime());
            record.set("create_by", getCurrentUser().getMobile());
            Db.save("eh_wx_bx", "id", record);

            // 更新文件关联
            try {
                // 方式1：如果有业务ID，更新相关文件的business_id
                if (!StringUtils.isEmpty(businessId)) {
                    Db.update("UPDATE eh_file_info SET business_id = ? WHERE business_id = ?", recordId, businessId);
                }

                // 方式2：解析media_urls中的fileId并更新关联
                String mediaUrls = params.getString("media_urls");
                if (!StringUtils.isEmpty(mediaUrls)) {
                    try {
                        com.alibaba.fastjson.JSONArray mediaArray = com.alibaba.fastjson.JSONArray.parseArray(mediaUrls);
                        for (int i = 0; i < mediaArray.size(); i++) {
                            com.alibaba.fastjson.JSONObject mediaObj = mediaArray.getJSONObject(i);
                            String fileId = mediaObj.getString("fileId");
                            if (!StringUtils.isEmpty(fileId)) {
                                Db.update("UPDATE eh_file_info SET business_id = ? WHERE file_id = ? AND business_type = 'bx'", recordId, fileId);
                            }
                        }
                    } catch (Exception parseError) {
                        logger.warn("解析media_urls失败: " + parseError.getMessage());
                    }
                }
            } catch (Exception fileUpdateError) {
                logger.warn("更新文件业务ID失败: " + fileUpdateError.getMessage());
            }

            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("新增失败: " + e.getMessage());
        }
    }


    @PostMapping("/history")
    public AjaxResult history() {
        try {
            Long wxUserId = getCurrentUserId();
            List<Record> list = Db.find("select id, type, content, address, create_time, status, media_urls, feedback, handler, handling_time from eh_wx_bx where owner_id = ? order by create_time desc", getCurrentUser().getOwnerId());
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record r : list) {
                Map<String, Object> m = new HashMap<>();
                m.put("id", r.get("id"));
                m.put("type", r.get("type"));
                m.put("content", r.get("content"));
                m.put("address", r.get("address"));
                m.put("createTime", r.get("create_time"));
                m.put("status", r.get("status"));
                m.put("media_urls", r.get("media_urls"));
                m.put("reply_content", r.get("feedback")); // 将feedback映射为reply_content保持前端兼容
                m.put("handler", r.get("handler")); // 处理人
                m.put("handling_time", r.get("handling_time")); // 处理时间
                result.add(m);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return AjaxResult.error("获取历史失败: " + e.getMessage());
        }
    }

    @PostMapping("/bxDetail")
    public AjaxResult bxDetail() {
        try {
            JSONObject params = getParams();
            String id = params.getString("id");
            if (StringUtils.isEmpty(id)) {
                return AjaxResult.error("参数错误");
            }

            // 查询报修详情，确保只能查看自己的报修记录
            Record record = Db.findFirst("select * from eh_wx_bx where id = ? and owner_id = ?", id, getCurrentUser().getOwnerId());
            if (record == null) {
                return AjaxResult.error("未找到报修记录");
            }

            return AjaxResult.success(record.toMap());
        } catch (Exception e) {
            logger.error("获取报修详情失败: " + e.getMessage(), e);
            return AjaxResult.error("获取报修详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/updateYsStatus")
    public AjaxResult updateYsStatus() {
        try {
            JSONObject params = getParams();
            String id = params.getString("id");
            int ysStatus = params.getIntValue("ysStatus");
            String ysReply = params.getString("ysReply");

            if (StringUtils.isEmpty(id) || ysStatus==0 || ysStatus > 2) {
                return AjaxResult.error("参数错误");
            }

            // 验证报修记录是否存在且属于当前用户
            Record record = Db.findFirst("select * from eh_wx_bx where id = ? and owner_id = ?", id, getCurrentUser().getOwnerId());
            if (record == null) {
                return AjaxResult.error("未找到报修记录");
            }

            // 检查当前状态是否为已完成(status=2)
            Integer currentStatus = record.getInt("status");
            if (currentStatus == null || currentStatus != 2) {
                return AjaxResult.error("当前状态不允许验收操作");
            }

            // 检查验收状态是否为待验收(ys_status=0)
            Integer currentYsStatus = record.getInt("ys_status");
            if (currentYsStatus == null) {
                currentYsStatus = 0; // 默认值为0
            }
            if (currentYsStatus != 0) {
                return AjaxResult.error("该工单已完成验收，不能重复验收");
            }

            // 更新验收状态
            StringBuilder sql = new StringBuilder("update eh_wx_bx set ys_status = ?, ys_time = ?, update_time = ?");
            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(ysStatus);
            sqlParams.add(DateUtils.getTime());
            sqlParams.add(DateUtils.getTime());

            // 如果有验收回复内容，则更新
            if (StringUtils.isNotEmpty(ysReply)) {
                sql.append(", ys_reply = ?");
                sqlParams.add(ysReply);
            }

            sql.append(" where id = ?");
            sqlParams.add(id);

            int updateCount = Db.update(sql.toString(), sqlParams.toArray());
            if (updateCount > 0) {
                return AjaxResult.success("验收操作成功");
            } else {
                return AjaxResult.error("验收操作失败");
            }
        } catch (Exception e) {
            logger.error("更新验收状态失败: " + e.getMessage(), e);
            return AjaxResult.error("更新验收状态失败: " + e.getMessage());
        }
    }

}
