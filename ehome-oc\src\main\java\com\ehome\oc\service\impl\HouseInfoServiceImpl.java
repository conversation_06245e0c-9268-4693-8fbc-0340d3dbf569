package com.ehome.oc.service.impl;

import com.ehome.common.exception.ServiceException;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.mapper.HouseInfoMapper;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HouseInfoServiceImpl implements IHouseInfoService {

    @Autowired
    private HouseInfoMapper houseInfoMapper;


    @Override
    public HouseInfo selectHouseById(Long houseId) {
        return houseInfoMapper.selectHouseById(houseId);
    }


    @Override
    public HouseInfo recordToObj(Record record){
        HouseInfo house = new HouseInfo();
        house.setHouseId(record.getLong("house_id"));
        house.setIsDefault(record.getInt("user_default"));
        house.setHouseStatus(record.getStr("house_status"));
        house.setCheckStatus(record.getInt("check_status"));
        house.setStatus(record.getInt("check_status"));
        house.setCommunityName(record.getStr("community_name"));
        house.setCommunityId(record.getStr("community_id"));
        house.setBuildingId(record.getStr("building_id"));
        house.setBuilding(record.getStr("building_name")); // 从查询结果中获取楼栋名称
        house.setUnit(record.getStr("unit_name")); // 从查询结果中获取单元名称
        house.setRoom(record.getStr("room"));
        house.setCombinaName(record.getStr("combina_name")); // 添加房屋全称
        house.setArea(record.getBigDecimal("area"));
        house.setOwnerName(record.getStr("owner_name"));
        house.setOwnerPhone(record.getStr("owner_phone"));

        house.setIdCard(record.getStr("id_card"));
        house.setRemark(record.getStr("remark"));

        // 设置住户信息字符串
        house.setOwnerStr(record.getStr("owner_str"));

        // 添加关系类型信息，用于前端显示住户类型
        // 使用houseStatus字段临时存储关系类型（因为这个字段在房屋列表中不常用）
        Integer relType = record.getInt("rel_type");
        if (relType != null) {
            house.setHouseStatus(relType.toString());
        }
        house.setHouseId(record.getLong("house_id"));
        house.setCombinaName(record.getStr("combina_name"));
        return house;
    }

    @Override
    @Transactional
    public int addHouse(HouseInfo house) {
        house.setCheckStatus(0);
        return houseInfoMapper.insertHouse(house);
    }

    @Override
    public int deleteHouse(Long houseId) {
        // 检查是否为默认房屋
        HouseInfo house = houseInfoMapper.selectHouseById(houseId);
        if (house != null && house.getIsDefault() == 1) {
            throw new ServiceException("默认房屋不能删除");
        }
        return houseInfoMapper.deleteHouseById(houseId);
    }

    @Override
    public int setDefaultHouse(Long houseId, String ownerId) {
        // 先重置该用户的所有默认房屋
        Db.update("UPDATE eh_house_owner_rel  SET is_default = 0 WHERE owner_id = ?", ownerId);
        String combinaName = Db.queryStr("select concat(combina_name,'/',floor,'/',room) from eh_house_info where house_id = ?", houseId);
        Db.update("update eh_owner t1 set house_id = ?,house_name = ? where owner_id = ?", houseId,combinaName,ownerId);
        // 设置新地默认房屋
        return Db.update("UPDATE eh_house_owner_rel SET is_default = 1 WHERE house_id = ? and owner_id = ? ", houseId, ownerId);
    }

    @Override
    public void updateOwnerHouseInfo(String ownerId) {
        // 更新业主的房屋数量 - 使用关联表计算实际数量
        Db.update("UPDATE eh_owner o SET house_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.owner_id = o.owner_id) WHERE o.owner_id = ?", ownerId);
        // 更新业主的房屋信息字符串
        Db.update("update eh_owner t1 set t1.house_info = (SELECT GROUP_CONCAT(CONCAT(t2.combina_name,'/',t2.room)) from eh_house_info t2 ,eh_house_owner_rel t3 where t3.house_id = t2.house_id and t1.owner_id = t3.owner_id) where t1.owner_id = ?", ownerId);
    }

    @Override
    public void updateHouseOwnerInfo(String houseId) {
        // 更新房屋的住户数量 - 使用关联表计算实际数量
        Db.update("UPDATE eh_house_info h SET owner_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.house_id = h.house_id) WHERE h.house_id = ?", houseId);
        // 更新房屋的业主字符串
        Db.update("UPDATE eh_house_info t1 JOIN ( SELECT t3.house_id, GROUP_CONCAT(t2.owner_name) AS owner_str FROM eh_owner t2 JOIN eh_house_owner_rel t3 ON t2.owner_id = t3.owner_id GROUP BY t3.house_id ) AS t4 ON t1.house_id = t4.house_id SET t1.owner_str = t4.owner_str WHERE t1.house_id = ?", houseId);
    }

    @Override
    public int countHouseByUserId(String ownerId) {
        return Db.queryInt("SELECT COUNT(*) FROM eh_house_owner_rel WHERE owner_id = ?", ownerId);
    }
} 