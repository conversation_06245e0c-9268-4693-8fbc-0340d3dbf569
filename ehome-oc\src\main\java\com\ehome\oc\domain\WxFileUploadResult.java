package com.ehome.oc.domain;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.DateUtils;

public class WxFileUploadResult {
    private String fileId;
    private String fileName;
    private String localUrl;
    private String ossUrl;
    private String ossKey;
    private String storageType;
    private String originalFilename;
    private long fileSize;
    private String fileType;
    private String mimeType;

    public WxFileUploadResult(String fileId, String fileName, String localUrl, String ossUrl, String ossKey,
                              String storageType, String originalFilename, long fileSize,
                              String fileType, String mimeType) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.localUrl = localUrl;
        this.ossUrl = ossUrl;
        this.ossKey = ossKey;
        this.storageType = storageType;
        this.originalFilename = originalFilename;
        this.fileSize = fileSize;
        this.fileType = fileType;
        this.mimeType = mimeType;
    }

    /**
     * 获取最终访问URL（优先OSS URL）
     */
    public String getFinalUrl() {
        return ossUrl != null ? ossUrl : localUrl;
    }

    // Getters
    public String getFileId() { return fileId; }
    public String getFileName() { return fileName; }
    public String getLocalUrl() { return localUrl; }
    public String getOssUrl() { return ossUrl; }
    public String getOssKey() { return ossKey; }
    public String getStorageType() { return storageType; }
    public String getOriginalFilename() { return originalFilename; }
    public long getFileSize() { return fileSize; }
    public String getFileType() { return fileType; }
    public String getMimeType() { return mimeType; }

    public JSONObject getJson(){
        JSONObject json = new JSONObject();
        json.put("fileId", fileId);
        json.put("fileName", fileName);
        json.put("localUrl", localUrl);
        json.put("ossUrl", ossUrl);
        json.put("ossKey", ossKey);
        json.put("storageType", storageType);
        json.put("originalFilename", originalFilename);
        json.put("fileSize", fileSize);
        json.put("fileType", fileType);
        json.put("mimeType", mimeType);
        json.put("finalUrl", getFinalUrl());
        json.put("uploadTime", DateUtils.getTime());
        return json;
    }
}
