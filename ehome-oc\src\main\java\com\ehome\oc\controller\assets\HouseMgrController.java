package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/oc/house")
public class HouseMgrController extends BaseController {

    private static final String PREFIX = "oc/house";

    @Autowired
    private IHouseInfoService houseInfoService;

    @GetMapping("/mgr")
    public String mgr(String buildingId, String unitId, ModelMap mmap) {
        if (StringUtils.isNotEmpty(buildingId)) {
            mmap.put("buildingId", buildingId);
        }else{
            mmap.put("buildingId", "");
        }
        if (StringUtils.isNotEmpty(unitId)) {
            mmap.put("unitId", unitId);
        }else{
            mmap.put("unitId", "");
        }
        return PREFIX + "/list";
    }



    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select t1.*,t2.name as building_name,t3.name as unit_name",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String houseId = params.getString("house_id");
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        Record house = Db.findFirst("select * from eh_house_info where house_id = ?", houseId);
        return AjaxResult.success(null, house.toMap());
    }

    @GetMapping("/add")
    public String add(String buildingId, String unitId, ModelMap mmap) {
        if (StringUtils.isNotEmpty(buildingId)) {
            mmap.put("buildingId", buildingId);
        }else{
            mmap.put("buildingId", "");
        }
        if (StringUtils.isNotEmpty(unitId)) {
            mmap.put("unitId", unitId);
        }else{
            mmap.put("unitId", "");
        }
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{houseId}")
    public String edit(@PathVariable("houseId") String houseId, ModelMap mmap) {
        Record house = Db.findFirst("select * from eh_house_info where house_id = ?", houseId);
        mmap.put("house", house.toMap());
        return PREFIX + "/edit";
    }

    @Log(title = "新增房屋", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        Record house = new Record();
        house.setColumns(params);
        setCreateAndUpdateInfo(house);
        Db.save("eh_house_info", "house_id", house);
        return AjaxResult.success();
    }

    @Log(title = "修改房屋", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record house = new Record();
        params.remove("buildingId");
        house.setColumns(params);
        setUpdateInfo(house);
        return toAjax(Db.update("eh_house_info", "house_id", house));
    }

    @Log(title = "删除房屋", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById("eh_house_info", "house_id", id);
        }
        return success();
    }

    /**
     * 查看房屋绑定的住户页面
     */
    @GetMapping("/owners/{houseId}")
    public String owners(@PathVariable("houseId") String houseId, ModelMap mmap) {
        mmap.put("houseId", houseId);
        return PREFIX + "/owners";
    }

    /**
     * 房屋绑定管理页面
     */
    @GetMapping("/houseBindings/{houseId}")
    public String houseBindings(@PathVariable("houseId") String houseId, ModelMap mmap) {
        mmap.put("houseId", houseId);
        return PREFIX + "/house-bindings";
    }

    /**
     * 获取房屋绑定数据
     */
    @PostMapping("/getHouseBindingData")
    @ResponseBody
    public AjaxResult getHouseBindingData() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }

        try {
            Map<String, Object> result = new HashMap<>();

            // 获取房屋基本信息
            Record house = Db.findFirst(
                "SELECT h.*, b.name as building_name, u.name as unit_name " +
                "FROM eh_house_info h " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "WHERE h.house_id = ?",
                houseId
            );

            if (house != null) {
                String houseAddress = (house.getStr("building_name") != null ? house.getStr("building_name") : "") +
                                    "/" + (house.getStr("unit_name") != null ? house.getStr("unit_name") : "") +
                                    "/" + (house.getStr("room") != null ? house.getStr("room") : "");
                result.put("houseAddress", houseAddress);
                result.put("houseArea", house.getBigDecimal("use_area"));
                result.put("houseFloor", house.getStr("floor"));

                // 房屋状态转换
                String houseStatus = house.getStr("house_status");
                String statusText = "未知";
                if ("2001".equals(houseStatus)) statusText = "已入住";
                else if ("2002".equals(houseStatus)) statusText = "未销售";
                else if ("2003".equals(houseStatus)) statusText = "已交房";
                else if ("2004".equals(houseStatus)) statusText = "未入住";
                else if ("2005".equals(houseStatus)) statusText = "已装修";
                else if ("2009".equals(houseStatus)) statusText = "装修中";
                result.put("houseStatus", statusText);
            }

            // 获取绑定的业主列表
            List<Record> owners = Db.find(
                "SELECT r.*, o.owner_name, o.mobile, o.id_card " +
                "FROM eh_house_owner_rel r " +
                "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                "WHERE r.house_id = ? " +
                "ORDER BY r.is_default DESC, r.create_time DESC",
                houseId
            );
            result.put("owners", recordToMap(owners));

            // 获取绑定的车位列表
            List<Record> parkings = Db.find(
                "SELECT r.*, p.parking_no, p.parking_type " +
                "FROM eh_parking_house_rel r " +
                "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                "WHERE r.house_id = ? " +
                "ORDER BY r.create_time DESC",
                houseId
            );
            result.put("parkings", recordToMap(parkings));

            // 获取绑定的车辆列表
            List<Record> vehicles = Db.find(
                "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model " +
                "FROM eh_vehicle_house_rel r " +
                "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                "WHERE r.house_id = ? " +
                "ORDER BY r.create_time DESC",
                houseId
            );
            result.put("vehicles", recordToMap(vehicles));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋绑定数据失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取可绑定的业主列表
     */
    @PostMapping("/getAvailableOwners")
    @ResponseBody
    public AjaxResult getAvailableOwners() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT owner_id as id, CONCAT(owner_name, ' (', mobile, ')') as text " +
                        "FROM eh_owner " +
                        "WHERE community_id = ? " +
                        "AND owner_id NOT IN (SELECT owner_id FROM eh_house_owner_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND (owner_name LIKE ? OR mobile LIKE ?) ";
                sqlParams.add("%" + search + "%");
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY owner_name LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定业主列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取可绑定的车位列表
     */
    @PostMapping("/getAvailableParkings")
    @ResponseBody
    public AjaxResult getAvailableParkings() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT parking_id as id, parking_no as text " +
                        "FROM eh_parking_space " +
                        "WHERE community_id = ? " +
                        "AND parking_id NOT IN (SELECT parking_id FROM eh_parking_house_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND parking_no LIKE ? ";
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY parking_no LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定车位列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取可绑定的车辆列表
     */
    @PostMapping("/getAvailableVehicles")
    @ResponseBody
    public AjaxResult getAvailableVehicles() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String houseId = params.getString("houseId");
        int page = params.containsKey("page") ? params.getIntValue("page") : 1;
        int pageSize = 20;

        try {
            String sql = "SELECT vehicle_id as id, plate_no as text " +
                        "FROM eh_vehicle " +
                        "WHERE community_id = ? " +
                        "AND vehicle_id NOT IN (SELECT vehicle_id FROM eh_vehicle_house_rel WHERE house_id = ?) ";

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(getSysUser().getCommunityId());
            sqlParams.add(houseId);

            if (StringUtils.isNotEmpty(search)) {
                sql += "AND plate_no LIKE ? ";
                sqlParams.add("%" + search + "%");
            }

            sql += "ORDER BY plate_no LIMIT ?, ?";
            sqlParams.add((page - 1) * pageSize);
            sqlParams.add(pageSize);

            List<Record> list = Db.find(sql, sqlParams.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("data", recordToMap(list));
            result.put("total", list.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取可绑定车辆列表失败", e);
            return AjaxResult.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 添加业主绑定
     */
    @PostMapping("/addOwnerBinding")
    @ResponseBody
    @Log(title = "绑定业主", businessType = BusinessType.INSERT)
    public AjaxResult addOwnerBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String ownerId = params.getString("ownerId");
        Integer relType = params.getInteger("relType");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (relType == null) {
            relType = 1; // 默认为业主
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                houseId, ownerId
            );

            if (existRel != null) {
                return AjaxResult.error("该业主已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("owner_id", ownerId);
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("rel_type", relType);
            rel.set("is_default", 0);
            rel.set("check_status", 1); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
            if (success) {
                // 更新房屋的绑定业主数量
                houseInfoService.updateHouseOwnerInfo(houseId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定业主失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解除业主绑定
     */
    @PostMapping("/removeOwnerBinding")
    @ResponseBody
    @Log(title = "解绑业主", businessType = BusinessType.DELETE)
    public AjaxResult removeOwnerBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_house_owner_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);
            if (success) {
                // 更新房屋的绑定业主数量
                String houseId = rel.getStr("house_id");
                houseInfoService.updateHouseOwnerInfo(houseId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑业主失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 添加车位绑定
     */
    @PostMapping("/addParkingBinding")
    @ResponseBody
    @Log(title = "绑定车位", businessType = BusinessType.INSERT)
    public AjaxResult addParkingBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_parking_house_rel WHERE house_id = ? AND parking_id = ?",
                houseId, parkingId
            );

            if (existRel != null) {
                return AjaxResult.error("该车位已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("community_id", getSysUser().getCommunityId());
            rel.set("house_id", houseId);
            rel.set("parking_id", parkingId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_parking_house_rel", "rel_id", rel);
            if (success) {
                // 同步更新车位表中的房屋信息
                updateParkingHouseInfo(parkingId, houseId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车位失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 更新车位表中的房屋信息
     */
    private void updateParkingHouseInfo(String parkingId, String houseId) {
        try {
            // 获取房屋信息
            Record house = Db.findFirst("SELECT combina_name,room FROM eh_house_info WHERE house_id = ?", houseId);
            if (house != null) {
                String combinaName = house.getStr("combina_name");
                String houseName = house.getStr("room");
                // 更新车位表中的房屋信息
                Db.update("UPDATE eh_parking_space SET house_id = ?, house_name = ? WHERE parking_id = ?",
                    houseId, combinaName+"/"+houseName, parkingId);
            }
        } catch (Exception e) {
            logger.error("更新车位房屋信息失败", e);
        }
    }

    /**
     * 解除车位绑定
     */
    @PostMapping("/removeParkingBinding")
    @ResponseBody
    @Log(title = "解绑车位", businessType = BusinessType.DELETE)
    public AjaxResult removeParkingBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_parking_house_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String parkingId = rel.getStr("parking_id");
            boolean success = Db.deleteById("eh_parking_house_rel", "rel_id", relId);
            if (success) {
                // 清空车位表中的房屋信息
                clearParkingHouseInfo(parkingId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 清空车位表中的房屋信息
     */
    private void clearParkingHouseInfo(String parkingId) {
        try {
            // 清空车位表中的房屋信息
            Db.update("UPDATE eh_parking_space SET house_id = NULL, house_name = '' WHERE parking_id = ?", parkingId);
        } catch (Exception e) {
            logger.error("清空车位房屋信息失败", e);
        }
    }

    /**
     * 添加车辆绑定
     */
    @PostMapping("/addVehicleBinding")
    @ResponseBody
    @Log(title = "绑定车辆", businessType = BusinessType.INSERT)
    public AjaxResult addVehicleBinding() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String vehicleId = params.getString("vehicleId");

        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_house_rel WHERE house_id = ? AND vehicle_id = ?",
                houseId, vehicleId
            );

            if (existRel != null) {
                return AjaxResult.error("该车辆已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("vehicle_id", vehicleId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            setCreateAndUpdateInfo(rel);

            boolean success = Db.save("eh_vehicle_house_rel", "rel_id", rel);
            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车辆失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解除车辆绑定
     */
    @PostMapping("/removeVehicleBinding")
    @ResponseBody
    @Log(title = "解绑车辆", businessType = BusinessType.DELETE)
    public AjaxResult removeVehicleBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_vehicle_house_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            boolean success = Db.deleteById("eh_vehicle_house_rel", "rel_id", relId);
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车辆失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取房屋详情
     */
    @PostMapping("/detail")
    @ResponseBody
    public AjaxResult detail() {
        JSONObject params = getParams();
        String houseId = params.getString("house_id");
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        
        // 查询房屋信息，包括楼栋和单元名称
        Record house = Db.findFirst(
            "SELECT h.*, b.name as buildingName, u.name as unitName " +
            "FROM eh_house_info h " +
            "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
            "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
            "WHERE h.house_id = ?", 
            houseId
        );
        
        if (house == null) {
            return AjaxResult.error("房屋不存在");
        }
        
        return AjaxResult.success(house.toMap());
    }
    
    /**
     * 查询房屋绑定的住户列表
     */
    @PostMapping("/ownerList")
    @ResponseBody
    public TableDataInfo ownerList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }
        
        // 构建查询SQL
        EasySQL sql = new EasySQL();
        sql.append("SELECT r.*, o.owner_name, o.mobile, o.id_card, o.gender, o.remark, o.is_live,o.house_info FROM eh_house_owner_rel r");
        sql.append("LEFT JOIN eh_owner o ON r.owner_id = o.owner_id");
        sql.append(houseId,"WHERE r.house_id = ?");
        
        // 添加查询条件
        sql.appendLike(params.getString("owner_name"), "AND o.owner_name LIKE ?");
        sql.append(params.getString("rel_type"), "AND r.rel_type = ?");
        sql.append(params.getString("check_status"), "AND r.check_status = ?");
        
        // 默认按是否默认和创建时间排序
        sql.append("ORDER BY r.is_default DESC, r.create_time DESC");
        
        // 执行查询
        List<Record> list = Db.find(sql.getSQL(),sql.getParams());
        
        return getDataList(list);
    }
    
    /**
     * 绑定住户
     */
    @Log(title = "绑定住户", businessType = BusinessType.INSERT)
    @PostMapping("/bindOwner")
    @ResponseBody
    public AjaxResult bindOwner() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String ownerId = params.getString("ownerId");
        Integer relType = params.getInteger("relType");
        Integer isDefault = params.getInteger("isDefault");
        String remark = params.getString("remark");
        
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("住户ID不能为空");
        }
        if (relType == null) {
            relType = 1; // 默认为业主
        }
        if (isDefault == null) {
            isDefault = 0; // 默认为非默认
        }
        
        // 检查是否已经绑定
        Record existRel = Db.findFirst(
            "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
            houseId, ownerId
        );
        
        if (existRel != null) {
            return AjaxResult.error("该住户已绑定到此房屋");
        }
        
        // 如果设置为默认，需要先将其他关系设为非默认
        if (isDefault == 1) {
            Db.update("UPDATE eh_house_owner_rel SET is_default = 0 WHERE owner_id = ?", ownerId);
            String combinaName = Db.queryStr("select combina_name from eh_house_info where house_id = ?", houseId);
            Db.update("update eh_owner t1 set house_id = ?,house_name = ? where owner_id = ?", houseId,combinaName,ownerId);
        }
        
        // 创建新的绑定关系
        Record rel = new Record();
        rel.set("rel_id", Seq.getId());
        rel.set("house_id", houseId);
        rel.set("owner_id", ownerId);
        rel.set("rel_type", relType);
        rel.set("is_default", isDefault);
        rel.set("check_status", 1); // 默认已审核
        rel.set("remark", remark);
        setCreateAndUpdateInfo(rel);
        
        boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
        if(success) {
            houseInfoService.updateHouseOwnerInfo(houseId);
            houseInfoService.updateOwnerHouseInfo(ownerId);
        }
        return toAjax(success);
    }
    
    /**
     * 解绑住户
     */
    @Log(title = "解绑住户", businessType = BusinessType.DELETE)
    @PostMapping("/unbindOwner")
    @ResponseBody
    public AjaxResult unbindOwner() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }
        // 查询关系信息
        Record rel = Db.findById("eh_house_owner_rel", "rel_id", relId);
        if (rel == null) {
            return AjaxResult.error("绑定关系不存在");
        }
        
        String houseId = rel.getStr("house_id");
        String ownerId = rel.getStr("owner_id");
        boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);
        if(success) {
            // 更新房屋的住户数量 - 使用关联表计算实际数量
            Db.update("UPDATE eh_house_info h SET owner_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.house_id = h.house_id) WHERE h.house_id = ?", houseId);
            // 更新业主的房屋数量 - 使用关联表计算实际数量
            Db.update("UPDATE eh_owner o SET house_count = (SELECT COUNT(*) FROM eh_house_owner_rel r WHERE r.owner_id = o.owner_id) WHERE o.owner_id = ?", ownerId);
            // 更新房屋的业主字符串
            Db.update("UPDATE eh_house_info t1 JOIN ( SELECT t3.house_id, GROUP_CONCAT(t2.owner_name) AS owner_str FROM eh_owner t2 JOIN eh_house_owner_rel t3 ON t2.owner_id = t3.owner_id GROUP BY t3.house_id ) AS t4 ON t1.house_id = t4.house_id SET t1.owner_str = t4.owner_str WHERE t1.house_id = ?", houseId);
            // 更新业主的房屋信息字符串
            Db.update("update eh_owner t1 set t1.house_info = (SELECT GROUP_CONCAT(CONCAT(t2.combina_name,'/',t2.room)) from eh_house_info t2 ,eh_house_owner_rel t3 where t3.house_id = t2.house_id and t1.owner_id = t3.owner_id) where t1.owner_id = ?", ownerId);
        }
        return toAjax(success);
    }
    
    /**
     * 设置默认住户
     */
    @Log(title = "设置默认住户", businessType = BusinessType.UPDATE)
    @PostMapping("/setDefaultOwner")
    @ResponseBody
    public AjaxResult setDefaultOwner() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        Long houseId = params.getLong("houseId");
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("ownerId不能为空");
        }

        if (houseId==null||houseId==0) {
            return AjaxResult.error("房屋ID不能为空");
        }
        int resultRow = houseInfoService.setDefaultHouse(houseId, ownerId);
        return toAjax(resultRow>0);

    }

    /**
     * 审核住户
     */
    @Log(title = "审核住户", businessType = BusinessType.UPDATE)
    @PostMapping("/approveOwner")
    @ResponseBody
    public AjaxResult approveOwner() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        String checkStatus = params.getString("checkStatus");

        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }
        if (StringUtils.isEmpty(checkStatus)) {
            return AjaxResult.error("审核状态不能为空");
        }

        try {
            // 查询关系信息
            Record rel = Db.findById("eh_house_owner_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            // 更新审核状态
            rel.set("check_status", checkStatus);
            setUpdateInfo(rel);

            boolean success = Db.update("eh_house_owner_rel", "rel_id", rel);
            return toAjax(success);
        } catch (Exception e) {
            logger.error("审核住户失败", e);
            return AjaxResult.error("审核失败：" + e.getMessage());
        }
    }

    @PostMapping("/queryCommunity")
    @ResponseBody
    public AjaxResult queryCommunity() {
        List<Record> list = Db.find("select * from eh_community where pms_id = ?", getSysUser().getPmsId());
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("oc_id"), record.getStr("oc_name"));
        });
        return AjaxResult.success(map);
    }

    @PostMapping("/queryBuilding")
    @ResponseBody
    public AjaxResult queryBuilding() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();
        if (StringUtils.isEmpty(communityId)) {
            return AjaxResult.error("小区ID不能为空");
        }
        List<Record> list = Db.find("SELECT t1.building_id,t1.name from eh_building t1 where t1.community_id = ?", communityId);
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("building_id"), record.getStr("name"));
        });
        return AjaxResult.success(map);
    }

    @PostMapping("/queryUnit")
    @ResponseBody
    public AjaxResult queryUnit() {
        JSONObject params = getParams();
        String buildingId = params.getString("buildingId");
        if (StringUtils.isEmpty(buildingId)) {
            return AjaxResult.error("楼栋ID不能为空");
        }
        List<Record> list = Db.find("SELECT t1.unit_id,t1.name from eh_unit t1 where t1.building_id = ?", buildingId);
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("unit_id"), record.getStr("name"));
        });
        return AjaxResult.success(map);
    }

    /**
     * 房屋选择弹窗页面
     */
    @GetMapping("/selectDialog")
    public String selectDialog() {
        return PREFIX + "/selectDialog";
    }

    /**
     * 房屋选择弹窗数据接口
     */
    @RequestMapping("/selectList")
    @ResponseBody
    public AjaxResult selectList() {
        JSONObject params = getParams();
        String keyword = params.getString("keyword");
        String sql = "SELECT house_id, room, building_id, unit_id FROM eh_house_info WHERE 1=1";
        if (StringUtils.isNotEmpty(keyword)) {
            sql += " AND room LIKE ?";
            List<Record> list = Db.find(sql, "%" + keyword + "%");
            return AjaxResult.success(recordToMap(list));
        } else {
            List<Record> list = Db.find(sql);
            return AjaxResult.success(recordToMap(list));
        }
    }

    /**
     * 查询房屋绑定的车辆列表
     */
    @RequestMapping("/vehicleList")
    @ResponseBody
    public TableDataInfo vehicleList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }
        // 查询车辆绑定关系及车辆信息（不再排序is_default）
        String sql = "SELECT r.rel_id, r.house_id, r.vehicle_id, r.check_status, r.remark, r.create_by, r.create_time, r.update_by, r.update_time, " +
                     "v.plate_no, v.vehicle_type, v.vehicle_brand, v.vehicle_model, v.owner_real_name, v.parking_space, v.remark as vehicle_remark " +
                     "FROM eh_vehicle_house_rel r " +
                     "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                     "WHERE r.house_id = ? " +
                     "ORDER BY r.create_time DESC";
        List<Record> list = Db.find(sql, houseId);
        return getDataList(list);
    }

    /**
     * 查询房屋绑定的车位列表
     */
    @RequestMapping("/parkingList")
    @ResponseBody
    public TableDataInfo parkingList() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        if (StringUtils.isEmpty(houseId)) {
            return getDataTable(new Page<>());
        }
        // 查询车位绑定关系及车位信息
        String sql = "SELECT r.rel_id, r.house_id, r.parking_id, r.check_status, r.remark, r.create_by, r.create_time, r.update_by, r.update_time, " +
                     "p.parking_no, p.parking_type, p.parking_status, p.parking_area, p.plate_no " +
                     "FROM eh_parking_house_rel r " +
                     "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                     "WHERE r.house_id = ? " +
                     "ORDER BY r.create_time DESC";
        List<Record> list = Db.find(sql, houseId);
        return getDataList(list);
    }

    /**
     * 绑定车辆
     */
    @Log(title = "绑定车辆", businessType = BusinessType.INSERT)
    @PostMapping("/bindVehicle")
    @ResponseBody
    public AjaxResult bindVehicle() {
        JSONObject params = getParams();
        String houseId = params.getString("houseId");
        String vehicleId = params.getString("vehicleId");
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        // 检查是否已绑定
        Record existRel = Db.findFirst(
            "SELECT * FROM eh_vehicle_house_rel WHERE house_id = ? AND vehicle_id = ?",
            houseId, vehicleId
        );
        if (existRel != null) {
            return AjaxResult.error("该车辆已绑定到此房屋");
        }
        // 创建新的绑定关系（不再处理is_default）
        Record rel = new Record();
        rel.set("rel_id", Seq.getId());
        rel.set("house_id", houseId);
        rel.set("vehicle_id", vehicleId);
        rel.set("check_status", 0); // 默认未审核
        setCreateAndUpdateInfo(rel);
        rel.remove("community_id");
        boolean success = Db.save("eh_vehicle_house_rel", "rel_id", rel);
        return toAjax(success);
    }

    /**
     * 解绑车辆
     */
    @Log(title = "解绑车辆", businessType = BusinessType.DELETE)
    @PostMapping("/unbindVehicle")
    @ResponseBody
    public AjaxResult unbindVehicle() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }
        Record rel = Db.findById("eh_vehicle_house_rel", "rel_id", relId);
        if (rel == null) {
            return AjaxResult.error("绑定关系不存在");
        }
        boolean success = Db.deleteById("eh_vehicle_house_rel", "rel_id", relId);
        return toAjax(success);
    }

    /**
     * 解绑车位
     */
    @Log(title = "解绑车位", businessType = BusinessType.DELETE)
    @PostMapping("/unbindParking")
    @ResponseBody
    public AjaxResult unbindParking() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            Record rel = Db.findById("eh_parking_house_rel", "rel_id", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String parkingId = rel.getStr("parking_id");
            boolean success = Db.deleteById("eh_parking_house_rel", "rel_id", relId);
            if (success) {
                // 清空车位表中的房屋信息
                clearParkingHouseInfo(parkingId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_house_info t1");
        sql.append("left join eh_building t2 on t1.building_id = t2.building_id");
        sql.append("left join eh_unit t3 on t1.unit_id = t3.unit_id");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?",false);
        sql.append(params.getString("buildingId"), "and t1.building_id = ?");
        sql.append(params.getString("unitId"), "and t1.unit_id = ?");
        sql.appendLike(params.getString("room"), "and t1.room like ?");
        sql.appendLike(params.getString("owner_name"), "and t1.owner_name like ?");
        sql.appendLike(params.getString("owner_phone"), "and t1.owner_phone like ?");

        // 添加搜索关键字支持，搜索楼栋号或单元号
        String searchKeyword = params.getString("searchKeyword");
        if (StringUtils.isNotEmpty(searchKeyword)) {
            sql.appendLike(searchKeyword, "and (t2.name like ? or t3.name like ?)");
        }
        sql.append(params.getString("status"), "and t1.status = ?");
        
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and t1.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and t1.create_time <= ?");

        sql.append("order by t1.create_time desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
}
