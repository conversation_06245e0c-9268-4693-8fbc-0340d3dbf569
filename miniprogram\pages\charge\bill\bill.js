const app = getApp()

Page({
  data: {
    // 页面状态
    loading: false,
    statusBarHeight: 20,
    
    // Tab相关
    activeTab: 'house',
    
    // 筛选条件
    filterYear: 'all',
    filterMonth: 'all', 
    filterStatus: 'all',
    yearOptions: [
      { text: '全部年份', value: 'all' },
      { text: '2024年', value: '2024' },
      { text: '2023年', value: '2023' }
    ],
    monthOptions: [
      { text: '全部月份', value: 'all' },
      { text: '1月', value: '01' },
      { text: '2月', value: '02' },
      { text: '3月', value: '03' },
      { text: '4月', value: '04' },
      { text: '5月', value: '05' },
      { text: '6月', value: '06' },
      { text: '7月', value: '07' },
      { text: '8月', value: '08' },
      { text: '9月', value: '09' },
      { text: '10月', value: '10' },
      { text: '11月', value: '11' },
      { text: '12月', value: '12' }
    ],
    statusOptions: [
      { text: '全部缴费状态', value: 'all' },
      { text: '正常', value: 'normal' },
      { text: '欠费', value: 'arrear' }
    ],
    
    // 数据
    buildingList: [],
    activeBuildingIds: [],
    totalArrearAmount: '0.00',
    totalArrearHouses: 0,
    
    // 水印相关
    showWatermark: false,
    watermarkConfig: null
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    if (!this.checkLoginStatus()) return
    this.loadBuildingList()
  },

  // 初始化页面
  initPage() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '小区缴费情况'
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (!token) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/mine/index'
          })
        }
      })
      return false
    }
    return true
  },

  // Tab切换
  onTabChange(event) {
    const tabName = event.detail.name
    this.setData({
      activeTab: tabName
    })
    
    if (tabName === 'house') {
      this.loadBuildingList()
    } else if (tabName === 'parking') {
      // 车位功能预留
      wx.showToast({
        title: '车位功能开发中',
        icon: 'none'
      })
    }
  },

  // 年份筛选
  onYearChange(event) {
    this.setData({
      filterYear: event.detail
    })
    this.loadBuildingList()
  },

  // 月份筛选
  onMonthChange(event) {
    this.setData({
      filterMonth: event.detail
    })
    this.loadBuildingList()
  },

  // 状态筛选
  onStatusChange(event) {
    this.setData({
      filterStatus: event.detail
    })
    this.loadBuildingList()
  },

  // 楼栋展开/收起
  onBuildingChange(event) {
    const activeBuildingIds = event.detail
    this.setData({
      activeBuildingIds
    })
    
    // 加载展开楼栋的房屋数据
    activeBuildingIds.forEach(buildingId => {
      this.loadBuildingHouseList(buildingId)
    })
  },

  // 加载楼栋列表
  async loadBuildingList() {
    if (!this.checkLoginStatus()) return
    
    try {
      this.setData({ loading: true })
      
      const res = await app.request({
        url: '/api/wx/charge/bill/getBuildingList',
        method: 'POST'
      })

      if (res.code === 0) {
        const { buildingList, totalArrearAmount, totalArrearHouses } = res.data
        
        this.setData({
          buildingList: buildingList || [],
          totalArrearAmount: totalArrearAmount || '0.00',
          totalArrearHouses: totalArrearHouses || 0
        })
      } else {
        throw new Error(res.msg || '获取楼栋列表失败')
      }
    } catch (error) {
      console.error('加载楼栋列表失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载楼栋房屋列表
  async loadBuildingHouseList(buildingId) {
    try {
      // 查找对应楼栋
      const buildingList = this.data.buildingList
      const buildingIndex = buildingList.findIndex(item => item.building_id === buildingId)
      
      if (buildingIndex === -1) return
      
      // 如果已经加载过，直接返回
      if (buildingList[buildingIndex].houseList) return
      
      // 设置加载状态
      buildingList[buildingIndex].loading = true
      this.setData({ buildingList })
      
      const res = await app.request({
        url: '/api/wx/charge/bill/getBuildingHouseList',
        method: 'POST',
        data: { buildingId }
      })

      if (res.code === 0) {
        buildingList[buildingIndex].houseList = res.data || []
        buildingList[buildingIndex].loading = false
        this.setData({ buildingList })
      } else {
        throw new Error(res.msg || '获取房屋列表失败')
      }
    } catch (error) {
      console.error('加载房屋列表失败:', error)
      
      // 清除加载状态
      const buildingList = this.data.buildingList
      const buildingIndex = buildingList.findIndex(item => item.building_id === buildingId)
      if (buildingIndex !== -1) {
        buildingList[buildingIndex].loading = false
        this.setData({ buildingList })
      }
      
      wx.showToast({
        title: error.message || '加载房屋数据失败',
        icon: 'none'
      })
    }
  }
})
