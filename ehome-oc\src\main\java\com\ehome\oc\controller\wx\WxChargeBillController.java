package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.PostMapping;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.math.BigDecimal;

@Controller
@RequestMapping("/wx/charge/bill")
public class WxChargeBillController extends BaseWxController {

    /**
     * 获取楼栋列表和统计信息
     */
    @PostMapping("/getBuildingList")
    @ResponseBody
    public AjaxResult getBuildingList() {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (communityId == null) {
                return AjaxResult.error("未找到小区信息");
            }

            // 查询楼栋列表及其房屋统计
            String sql = "SELECT " +
                    "b.building_id, " +
                    "b.name as building_name, " +
                    "COUNT(h.house_id) as total_houses, " +
                    "COUNT(CASE WHEN h.arrear_amount > 0 THEN 1 END) as arrear_houses, " +
                    "COALESCE(SUM(h.arrear_amount), 0) as total_arrear_amount " +
                    "FROM eh_building b " +
                    "LEFT JOIN eh_house_info h ON b.building_id = h.building_id " +
                    "WHERE b.community_id = ? " +
                    "GROUP BY b.building_id, b.name " +
                    "ORDER BY b.name";

            List<Record> buildingList = Db.find(sql, communityId);

            // 计算总体统计
            BigDecimal totalArrearAmount = BigDecimal.ZERO;
            int totalArrearHouses = 0;

            for (Record building : buildingList) {
                BigDecimal arrearAmount = building.getBigDecimal("total_arrear_amount");
                if (arrearAmount != null) {
                    totalArrearAmount = totalArrearAmount.add(arrearAmount);
                }
                Integer arrearHouses = building.getInt("arrear_houses");
                if (arrearHouses != null) {
                    totalArrearHouses += arrearHouses;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("buildingList", buildingList);
            result.put("totalArrearAmount", totalArrearAmount);
            result.put("totalArrearHouses", totalArrearHouses);

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取楼栋列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定楼栋的房屋缴费详情
     */
    @PostMapping("/getBuildingHouseList")
    @ResponseBody
    public AjaxResult getBuildingHouseList() {
        try {
            JSONObject params = getParams();
            String buildingId = params.getString("buildingId");

            if (buildingId == null) {
                return AjaxResult.error("楼栋ID不能为空");
            }

            String communityId = getCurrentUser().getCommunityId();
            if (communityId == null) {
                return AjaxResult.error("未找到小区信息");
            }

            // 查询楼栋下的房屋缴费详情
            String sql = "SELECT " +
                    "h.house_id, " +
                    "h.combina_name, " +
                    "h.room, " +
                    "h.arrear_amount, " +
                    "h.owner_str, " +
                    "CASE WHEN h.arrear_amount > 0 THEN '欠费' ELSE '正常' END as pay_status " +
                    "FROM eh_house_info h " +
                    "WHERE h.building_id = ? AND h.community_id = ? " +
                    "ORDER BY h.combina_name, h.room";

            List<Record> houseList = Db.find(sql, buildingId, communityId);

            return AjaxResult.success(houseList);
        } catch (Exception e) {
            return AjaxResult.error("获取房屋列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取整体缴费统计信息
     */
    @PostMapping("/getChargeStatistics")
    @ResponseBody
    public AjaxResult getChargeStatistics() {
        try {
            String communityId = getCurrentUser().getCommunityId();
            if (communityId == null) {
                return AjaxResult.error("未找到小区信息");
            }

            // 查询统计信息
            String sql = "SELECT " +
                    "COUNT(*) as total_houses, " +
                    "COUNT(CASE WHEN arrear_amount > 0 THEN 1 END) as arrear_houses, " +
                    "COUNT(CASE WHEN arrear_amount = 0 THEN 1 END) as normal_houses, " +
                    "COALESCE(SUM(arrear_amount), 0) as total_arrear_amount " +
                    "FROM eh_house_info " +
                    "WHERE community_id = ?";

            Record statistics = Db.findFirst(sql, communityId);

            return AjaxResult.success(statistics);
        } catch (Exception e) {
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }
}
