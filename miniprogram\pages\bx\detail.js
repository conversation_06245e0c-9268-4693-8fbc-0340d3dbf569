import fileAccessManager from '../../utils/fileAccessManager.js'

const app = getApp();

Page({
  data: {
    detail: null,
    loading: true,
    timeDiff: ''
  },

  // 计算时间差
  calculateTimeDiff(createTime) {
    if (!createTime) return '';

    const now = new Date();
    const create = new Date(createTime);
    const diff = now - create;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    this.getDetail(id);
  },

  // 获取报修详情
  async getDetail(id) {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/bx/bxDetail',
        method: 'POST',
        data: { id }
      });

      if (res.code === 0 && res.data) {
        const detail = this.processDetailData(res.data);
        const timeDiff = this.calculateTimeDiff(detail.create_time);
        this.setData({
          detail,
          timeDiff,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: '报修详情'
        });
      } else {
        throw new Error(res.msg || '获取详情失败');
      }
    } catch (error) {
      console.error('获取报修详情失败:', error);
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 处理详情数据
  processDetailData(data) {
    let mediaUrls = [];
    try {
      if (data.media_urls) {
        const parsed = JSON.parse(data.media_urls);
        mediaUrls = parsed.map(media => {
          const url = media.url || media;
          return {
            url: url,
            fileId: media.fileId,
            thumb: media.thumb || url,
            name: media.name || '附件',
            size: media.size || 0,
            sizeText: this.formatFileSize(media.size || 0),
            isImage: this.isImage(url)
          };
        });
      }
    } catch (e) {
      console.warn('解析media_urls失败:', e);
      mediaUrls = [];
    }

    // 处理验收状态显示 - 只有工单状态为已完成(2)时才处理验收状态
    let ysStatusText = '';
    let showYsInfo = false;
    if (data.status == 2) {
      const ysStatusMap = {
        0: '待验收',
        1: '验收通过',
        2: '验收不通过'
      };

      // ys_status默认值是0
      const ysStatus = data.ys_status || 0;
      ysStatusText = ysStatusMap[ysStatus] || '待验收';
      showYsInfo = true;
    }

    // 处理时间格式
    let formattedYsTime = '';
    if (data.ys_time) {
      formattedYsTime = this.formatDateTime(data.ys_time);
    }

    return {
      ...data,
      mediaUrls,
      statusText: this.getStatusText(data.status),
      statusColor: this.getStatusColor(data.status),
      reply_content: data.feedback || data.reply_content, // 将feedback映射为reply_content保持前端兼容
      ysStatusText: ysStatusText,
      showYsInfo: showYsInfo,
      ys_time: formattedYsTime || data.ys_time
    };
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '未处理',
      '1': '处理中',
      '2': '已完成'
    };
    return statusMap[status] || '未处理';
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',  // 红色
      '1': '#faad14',  // 橙色
      '2': '#52c41a'   // 绿色
    };
    return colorMap[status] || '#ff4d4f';
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size === 0) return '';
    if (size < 1024) return size + 'B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB';
    return (size / 1024 / 1024).toFixed(1) + 'MB';
  },

  // 判断是否为图片
  isImage(url) {
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUrl = url.toLowerCase();
    return imageExts.some(ext => lowerUrl.includes(ext));
  },

  // 附件点击处理
  onAttachmentTap(e) {
    const { url } = e.currentTarget.dataset;
    
    if (this.isImage(url)) {
      this.onPreviewImage(e);
    } else {
      this.onDownloadFile(e);
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { detail } = this.data;

    // 提取所有图片URL用于预览
    const imageUrls = detail.mediaUrls
      .filter(media => this.isImage(media.url))
      .map(media => media.url);

    if (imageUrls.length > 0) {
      wx.previewImage({
        current: url,
        urls: imageUrls
      });
    }
  },

  // 下载文件
  async onDownloadFile(e) {
    const { url, name, fileId } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '下载中...' });

      // 使用文件访问管理器预览文件
      await fileAccessManager.previewFile(fileId, url, name);

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('文件下载/打开失败:', error);
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 验收处理
  async onAcceptance(e) {
    const result = e.currentTarget.dataset.result;
    const resultText = result == 1 ? '通过' : '不通过';
    let replyContent = '';

    try {
      // 验收通过和不通过都需要填写意见
      const defaultContent = result == 1 ? '验收通过' : '';
      const title = result == 1 ? '验收通过' : '验收不通过';
      const placeholder = result == 1 ? '请填写验收意见（可选）：' : '请填写不通过的理由：';

      const inputRes = await this.showInputModal(title, placeholder, defaultContent);
      if (!inputRes.confirm) return;

      // 验收不通过必须填写理由
      if (result == 0 && !inputRes.content.trim()) {
        wx.showToast({
          title: '请填写不通过理由',
          icon: 'none'
        });
        return;
      }

      replyContent = inputRes.content.trim() || (result == 1 ? '验收通过' : '');

      const res = await wx.showModal({
        title: '确认验收',
        content: `确定验收${resultText}吗？`,
        confirmText: '确定',
        cancelText: '取消'
      });

      if (!res.confirm) return;

      wx.showLoading({ title: '处理中...' });

      const response = await app.request({
        url: '/api/wx/bx/updateYsStatus',
        method: 'POST',
        data: {
          id: this.data.detail.id,
          ysStatus: result,
          ysReply: replyContent
        }
      });

      if (response.code === 0) {
        wx.showToast({
          title: `验收${resultText}成功`,
          icon: 'success'
        });

        // 刷新详情
        setTimeout(() => {
          this.getDetail(this.data.detail.id);
        }, 1500);
      } else {
        throw new Error(response.msg || '验收失败');
      }
    } catch (error) {
      console.error('验收失败:', error);
      wx.showToast({
        title: error.message || '验收失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 显示输入框
  showInputModal(title, placeholder, defaultContent = '') {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        placeholderText: placeholder,
        content: defaultContent,
        editable: true,
        success: (res) => {
          resolve(res);
        },
        fail: () => {
          resolve({ confirm: false });
        }
      });
    });
  },

  // 格式化日期时间
  formatDateTime(dateTime) {
    if (!dateTime) return '';

    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.warn('格式化时间失败:', error);
      return dateTime;
    }
  }
});
